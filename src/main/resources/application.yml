spring:
  application:
    name: <PERSON><PERSON><PERSON>s

  data:
    mongodb:
      uri: mongodb://chollisimos_user:chollisi<PERSON>_password@localhost:27017/chollisimos

  thymeleaf:
    cache: false

  devtools:
    restart:
      enabled: true
      poll-interval: 2s
      quiet-period: 1s
    livereload:
      enabled: true
      port: 35729

server:
  port: 8080

logging:
  level:
    com.adrianheras.chollisimos: DEBUG
    org.springframework.security: DEBUG
