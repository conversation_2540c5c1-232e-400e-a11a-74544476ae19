spring:
  application:
    name: Chollisimos

  data:
    mongodb:
      # Usar variable de entorno si está disponible, sino usar localhost para desarrollo local
      uri: ${SPRING_DATA_MONGODB_URI:***************************************************************************}

  thymeleaf:
    cache: false

  devtools:
    restart:
      enabled: true
      poll-interval: 1s
      quiet-period: 400ms
      additional-paths: src/main
    livereload:
      enabled: true
      port: 35729
    remote:
      restart:
        enabled: true

server:
  port: 8080

logging:
  level:
    com.adrianheras.chollisimos: DEBUG
    org.springframework.security: DEBUG
    org.springframework.boot.devtools: DEBUG
