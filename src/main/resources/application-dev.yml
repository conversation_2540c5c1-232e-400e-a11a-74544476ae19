spring:
  application:
    name: <PERSON><PERSON><PERSON><PERSON>

  data:
    mongodb:
      uri: ***************************************************************************
  
  thymeleaf:
    cache: false
    
  devtools:
    restart:
      enabled: true
      poll-interval: 1s
      quiet-period: 400ms
      additional-paths: src/main
    livereload:
      enabled: true
      port: 35729
    remote:
      restart:
        enabled: true
        
server:
  port: 8080

logging:
  level:
    com.adrianheras.chollisimos: DEBUG
    org.springframework.security: DEBUG
    org.springframework.boot.devtools: DEBUG
