spring:
  application:
    name: Chollisimos

  data:
    mongodb:
      uri: ${SPRING_DATA_MONGODB_URI:mongodb://mongodb-service:27017/chollisimos_production}

  thymeleaf:
    cache: true

  # DevTools deshabilitado en producción
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false

server:
  port: 8080
  # Configuración de producción
  compression:
    enabled: true
  http2:
    enabled: true

logging:
  level:
    com.adrianheras.chollisimos: INFO
    org.springframework.security: WARN
    org.springframework.web: WARN
    org.hibernate: WARN
    org.springframework.boot.devtools: OFF
  file:
    name: /app/logs/chollisimos.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
