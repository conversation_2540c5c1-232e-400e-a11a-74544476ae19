
services:

  # INSTRUCCIONES
  # HAY QUE entrar en el contenedor del mongo
#  mongosh
#  use admin
#  db.auth("chollisimos_user_1", "chollisimos_password_1")
#
#  use chollisimos
#  db.createUser({
#  user: "chollisimos_123",
#  pwd: "chollisimos_ADVxcdwqq324532",
#  roles: [
#    { role: "readWrite", db: "chollisimos" }
#  ]
#})


  # PARA DESARROLLAR:

#  mvn clean compile package

#  docker cp target/chollisimos-0.0.1-SNAPSHOT.jar chollisimos_web-app-1:/app.jar && docker restart chollisimos_web-app-1

# aunque solo funciona si: docker-compose up -d --build

#  mvn spring-boot:run

# docker-compose up -d --build   -> sólo construye lo que detecte nuevo en el Dockerfile
# docker-compose up -d --force-recreate --build   -> reconstruye el Dockerfile entero

  # Aplicación Spring Boot
  app:
    build: .
    platform: linux/amd64
    restart: unless-stopped
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      # se aplicará el application-dev.yml
      SPRING_PROFILES_ACTIVE: dev
      SPRING_DATA_MONGODB_URI: *****************************************************************/chollisimos?authSource=admin
      SERVER_PORT: 8080
    ports:
      - "8080:8080"
      - "35729:35729"  # Puerto para LiveReload de DevTools
#    networks:
#      - chollisimos-network
    volumes:
      - app_logs:/app/logs
      - .:/app
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/actuator/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Base de datos MongoDB
  mongodb:
    image: mongo:7.0
    restart: unless-stopped
    environment:
      # solo valen para hacer db.auth (ver arriba) o si pones ?authSource=admin (usuario de db admin, no de db chollisimos)
      MONGO_INITDB_ROOT_USERNAME: chollisimos_user_1
      MONGO_INITDB_ROOT_PASSWORD: chollisimos_password_1
      MONGO_INITDB_DATABASE: chollisimos
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
#    networks:
#      - chollisimos-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cliente MongoDB de consola (servicio permanente)
  mongodb-client:
    # para escribir queries
    # mongosh *******************************************************************************
    # mongosh *****************************************************************/chollisimos?authSource=admin
    # show dbs, use chollisimos, show collections
    image: mongo:7.0
    restart: unless-stopped
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      MONGO_HOST: mongodb
      MONGO_PORT: 27017
      MONGO_DATABASE: chollisimos
      MONGO_USERNAME: chollisimos_user_1
      MONGO_PASSWORD: chollisimos_password_1
#    networks:
#      - chollisimos-network
    # Este contenedor se mantiene corriendo para poder ejecutar comandos
    command: tail -f /dev/null

  # Mongo Express para administración de base de datos (opcional)
#  mongo-express:
#    image: mongo-express:latest
#    restart: unless-stopped
#    depends_on:
#      - mongodb
#    ports:
#      - "8081:8081"
##    networks:
##      - chollisimos-network
#    environment:
#      ME_CONFIG_MONGODB_ADMINUSERNAME: chollisimos_user_1
#      ME_CONFIG_MONGODB_ADMINPASSWORD: chollisimos_password_1
#      ME_CONFIG_MONGODB_URL: *****************************************************************/

# Volúmenes persistentes
volumes:
  mongodb_data:
    driver: local
  app_logs:
    driver: local

# Red personalizada
#networks:
#  chollisimos-network:
#    driver: bridge
