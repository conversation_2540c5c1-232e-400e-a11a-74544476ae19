services:
  app:
    environment:
      SPRING_PROFILES_ACTIVE: prod
      SPRING_DATA_MONGODB_URI: ${SPRING_DATA_MONGODB_URI:-*********************************************************************************************************}
      SERVER_PORT: 8080
      # Variables de producción
      JAVA_OPTS: "-Xmx512m -Xms256m"
    # En producción no montamos el código fuente
    volumes:
      - app_logs:/app/logs
    ports:
      - "8080:8080"
    # Configuración más estricta para producción
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s
