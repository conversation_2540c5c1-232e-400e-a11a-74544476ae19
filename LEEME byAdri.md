# Configuración de Jenkins y despliegue con Docker y Kubernetes

build,execution,deployment / compiler / build project automatically

en los logs del contenedor:

  LiveReload server is running on port 35729


## 1. Subir imagen a repositorio privado en GitHub y DockerHub

```bash
<NAME_EMAIL>:soria86/mirepo1.git
docker build -t soria86/mirepo1:tagname .
docker login
docker push soria86/mirepo1:tagname
# mazo comandos de kubectl
```

## 2. DockerHub: credenciales

Ten tus credenciales del repo privado de DockerHub listas.  
> (Creo que solo un repo privado por cuenta, o no... hmm)

## 3. Jenkins / Panel de control / Administrar Jenkins

### Credenciales

**Obs.:** Las credenciales del repo Git son el token y se incluye a piñón en el script Jenkins.

#### Agregar credencial (DockerHub)

- **Domain**: global (en todo Jenkins estén disponibles)  
- **Store**: system  
- **ID**: `docker-credentials-id`  
- **Name**: ****  
- **Username**: ____  
- **Password**: _____  
- **Guardar**

#### Añadir credenciales de Kubernetes

- Igual que la anterior  
- **Tipo de credencial**: archivo  
- Selecciona el archivo kubeconfig  
- **ID**: `kubeconfig-credentials-id`  
- **Guardar**

#### Editar configuración de carpeta `k8s`

Renombrar "chollisimos" por otro (que sea igual a la variable de Jenkinsfile que pongas K8S_DEPLOY_NAME)

#### Actualizar `Jenkinsfile`

- Echa un ojo en el archivo `Jenkinsfile`, arriba, en las variables de entorno y comentarios
- En principio sólo hay que editar la variable K8S_DEPLOY_NAME
- Busqueda global por /chollisimos/i

## 4. Crear nueva tarea de Jenkins

- Nueva TAREA: `chollisimos.com`
- Sección: **Pipeline**
- `Pipeline` / `Definition`: *Pipeline script from SCM*
  - SCM: Git
  - Origen del código fuente git:
  - https://github.com/adrihm/chollisimos.com.git
  - https://<EMAIL>/chollisimos/chollisimos.git0
  - TIP: Echa un ojo en https://github.com/settings/tokens para ver el valor del token
    - Token clásico con permisos de "repo"
  - CREDENTIALS: NONE
  
- Branches to build: */main
- Script Path: Jenkinsfile
- Apply & Save

## 5. Ejecutar la tarea Jenkins

- Ejecuta la tarea desde el panel de Jenkins
